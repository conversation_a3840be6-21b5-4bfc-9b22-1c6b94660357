import type { Config } from 'tailwindcss'


const config: Config = {
    darkMode: ['class'],
    content: [
        './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
        './src/components/**/*.{js,ts,jsx,tsx,mdx}',
        './src/app/**/*.{js,ts,jsx,tsx,mdx}',
        // './src/**/*.{js,jsx,ts,tsx}',
        './node_modules/react-tailwindcss-datepicker/dist/index.esm.js',
    ],
    theme: {
        extend: {
            backgroundImage: {
                'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
                'gradient-conic':
                    'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
            },
            zIndex: {
                '100': '100',
            },
            fontFamily: {
                sans: ['IBMPlexSans', 'sans-serif'],
                italic: ['IBMPlexSans-italic', 'sans-serif'],
            },
            keyframes: {
                'accordion-down': {
                    from: {
                        height: '0',
                    },
                    to: {
                        height: 'var(--radix-accordion-content-height)',
                    },
                },
                'accordion-up': {
                    from: {
                        height: 'var(--radix-accordion-content-height)',
                    },
                    to: {
                        height: '0',
                    },
                },
            },
            animation: {
                'accordion-down': 'accordion-down 0.2s ease-out',
                'accordion-up': 'accordion-up 0.2s ease-out',
            },
        },
        screens: {
            // Granular mobile breakpoints
            tiny: '320px', // Tiny phones (iPhone SE, ultra-small screens)
            small: '375px', // Small phones (iPhone 12/13 Mini)
            standard: {
                min: '414px', // ≥414px
                max: '430px', // ≤430px
            },
            phablet: '480px', // Larger mobile devices in portrait
            'tablet-sm': '600px', // Small tablets
            'tablet-md': '768px', // Medium tablets (iPad Mini / standard portrait iPads)
            'tablet-lg': '834px', // Large tablets (iPad Pro 11-inch in portrait)
            landscape: '1024px', // iPad in landscape, Grid and dense views start to work
            laptop: '1280px', // Small laptops
            desktop: '1536px', // Desktop screens

            // Legacy breakpoints for backward compatibility
            '2xs': '375px',
            xs: '480px',
            sm: '768px',
            md: '1024px',
            lg: '1280px',
            xl: '1536px',
            '2xl': '1536px',
        },
        colors: {
            'curious-blue': {
                '50': 'hsl(208, 87%, 97%)',
                '100': 'hsl(210, 81%, 94%)',
                '200': 'hsl(207, 83%, 86%)',
                '300': 'hsl(206, 83%, 74%)',
                '400': 'hsl(205, 82%, 54%)',
                '500': 'hsl(205, 78%, 48%)',
                '600': 'hsl(207, 86%, 39%)',
                '700': 'hsl(208, 85%, 32%)',
                '800': 'hsl(208, 79%, 27%)',
                '900': 'hsl(209, 70%, 24%)',
                '950': 'hsl(212, 70%, 16%)',
                // Ultra Dark Blue Theme Colors
                'ultra-dark-50': 'hsl(212, 75%, 8%)',   // Background
                'ultra-dark-100': 'hsl(212, 70%, 12%)', // Muted/Input
                'ultra-dark-200': 'hsl(212, 70%, 14%)', // Cards/Sidebar
                'ultra-dark-300': 'hsl(212, 72%, 16%)', // Popover
                'ultra-dark-400': 'hsl(212, 65%, 18%)', // Secondary
                'ultra-dark-500': 'hsl(212, 75%, 6%)',  // Borders (darkest)
            },
            'cinnabar': {
                '50': 'hsl(0, 100%, 97%)',
                '100': 'hsl(2, 100%, 94%)',
                '200': 'hsl(1, 100%, 89%)',
                '300': 'hsl(1, 100%, 81%)',
                '400': 'hsl(1, 100%, 70%)',
                '500': 'hsl(1, 97%, 60%)',
                '600': 'hsl(1, 83%, 54%)',
                '700': 'hsl(1, 85%, 42%)',
                '800': 'hsl(1, 80%, 35%)',
                '900': 'hsl(1, 72%, 31%)',
                '950': 'hsl(1, 85%, 15%)',
            },
            'wedgewood': {
                '50': 'hsl(216, 33%, 97%)',
                '100': 'hsl(210, 31%, 94%)',
                '200': 'hsl(209, 35%, 86%)',
                '300': 'hsl(206, 35%, 74%)',
                '400': 'hsl(206, 34%, 60%)',
                '500': 'hsl(205, 32%, 45%)',
                '600': 'hsl(207, 35%, 39%)',
                '700': 'hsl(209, 35%, 32%)',
                '800': 'hsl(207, 33%, 27%)',
                '900': 'hsl(208, 30%, 24%)',
                '950': 'hsl(211, 28%, 16%)',
            },
            'outer-space': {
                '50': 'hsl(200, 16%, 96%)',
                '100': 'hsl(206, 14%, 90%)',
                '200': 'hsl(196, 12%, 82%)',
                '300': 'hsl(204, 13%, 69%)',
                '400': 'hsl(201, 11%, 53%)',
                '500': 'hsl(201, 12%, 43%)',
                '600': 'hsl(205, 12%, 36%)',
                '700': 'hsl(207, 11%, 31%)',
                '800': 'hsl(210, 9%, 27%)',
                '900': 'hsl(210, 8%, 24%)',
                '950': 'hsl(210, 11%, 15%)',
            },
            'fire-bush': {
                '50': 'hsl(40, 90%, 96%)',
                '100': 'hsl(41, 82%, 89%)',
                '200': 'hsl(41, 83%, 77%)',
                '300': 'hsl(38, 83%, 65%)',
                '400': 'hsl(36, 83%, 54%)',
                '500': 'hsl(30, 80%, 50%)',
                '600': 'hsl(25, 81%, 44%)',
                '700': 'hsl(19, 78%, 37%)',
                '800': 'hsl(15, 71%, 31%)',
                '900': 'hsl(15, 67%, 26%)',
                '950': 'hsl(13, 78%, 14%)',
            },
            'bright-turquoise': {
                '50': 'hsl(166, 100%, 97%)',
                '100': 'hsl(168, 100%, 89%)',
                '200': 'hsl(170, 100%, 78%)',
                '300': 'hsl(171, 97%, 64%)',
                '400': 'hsl(173, 83%, 54%)',
                '500': 'hsl(174, 100%, 40%)',
                '600': 'hsl(175, 100%, 32%)',
                '700': 'hsl(176, 97%, 26%)',
                '800': 'hsl(177, 87%, 22%)',
                '900': 'hsl(175, 77%, 19%)',
                '950': 'hsl(179, 100%, 10%)',
            },

            warning: {
                DEFAULT: 'hsl(var(--warning) / <alpha-value>)',
                foreground: 'hsl(var(--warning-foreground) / <alpha-value>)',
            },

            destructive: {
                DEFAULT: 'hsl(var(--destructive) / <alpha-value>)',
                foreground:
                    'hsl(var(--destructive-foreground) / <alpha-value>)',
            },
            muted: {
                DEFAULT: 'hsl(var(--muted) / <alpha-value>)',
                foreground: 'hsl(var(--muted-foreground) / <alpha-value>)',
            },
            accent: {
                DEFAULT: 'hsl(var(--accent) / <alpha-value>)',
                foreground: 'hsl(var(--accent-foreground) / <alpha-value>)',
            },

            primary: {
                DEFAULT: 'hsl(var(--primary) / <alpha-value>)',
                foreground: 'hsl(var(--primary-foreground) / <alpha-value>)',
            },

            secondary: {
                DEFAULT: 'hsl(var(--secondary) / <alpha-value>)',
                foreground: 'hsl(var(--secondary-foreground) / <alpha-value>)',
            },

            sidebar: {
                DEFAULT: 'hsl(var(--sidebar) / <alpha-value>)',
                foreground: 'hsl(var(--sidebar-foreground) / <alpha-value>)',
                primary: 'hsl(var(--sidebar-primary) / <alpha-value>)',
                'primary-foreground':
                    'hsl(var(--sidebar-primary-foreground) / <alpha-value>)',
                accent: 'hsl(var(--sidebar-accent) / <alpha-value>)',
                border: 'hsl(var(--sidebar-border) / <alpha-value>)',
                ring: 'hsl(var(--sidebar-ring) / <alpha-value>)',
            },

            popover: {
                DEFAULT: 'hsl(var(--popover) / <alpha-value>)',
                foreground: 'hsl(var(--foreground) / <alpha-value>)',
            },

            card: {
                DEFAULT: 'hsl(var(--card) / <alpha-value>)',
                foreground: 'hsl(var(--card-foreground) / <alpha-value>)',
            },

            background: {
                DEFAULT: 'hsl(var(--background) / <alpha-value>)',
                foreground: 'hsl(var(--foreground) / <alpha-value>)',
            },

            border: {
                DEFAULT: 'hsl(var(--border) / <alpha-value>)',
            },

            input: {
                DEFAULT: 'hsl(var(--input) / <alpha-value>)',
            },

            ring: {
                DEFAULT: 'hsl(var(--ring) / <alpha-value>)',
            },

            // Colors that need dark mode support (CSS custom properties)
            'neutral-400': {
                DEFAULT: 'hsl(var(--neutral-400) / <alpha-value>)',
            },
            'neutral-600': {
                DEFAULT: 'hsl(var(--neutral-600) / <alpha-value>)',
            },
            'neutral-800': {
                DEFAULT: 'hsl(var(--neutral-800) / <alpha-value>)',
            },
            'warning-100': {
                DEFAULT: 'hsl(var(--warning-100) / <alpha-value>)',
            },
            'success-100': {
                DEFAULT: 'hsl(var(--success-100) / <alpha-value>)',
            },
            'destructive-200': {
                DEFAULT: 'hsl(var(--destructive-200) / <alpha-value>)',
            },
        },

        keyframes: {
            'accordion-down': {
                from: {
                    height: '0',
                },
                to: {
                    height: 'var(--radix-accordion-content-height)',
                },
            },
            'accordion-up': {
                from: {
                    height: 'var(--radix-accordion-content-height)',
                },
                to: {
                    height: '0',
                },
            },
            'caret-blink': {
                '0%,70%,100%': {
                    opacity: '1',
                },
                '20%,50%': {
                    opacity: '0',
                },
            },
            spin: {
                to: {
                    transform: 'rotate(360deg)',
                },
            },
        },
        animation: {
            spin: 'spin 1s linear infinite',
            'accordion-down': 'accordion-down 0.2s ease-out',
            'accordion-up': 'accordion-up 0.2s ease-out',
            'caret-blink': 'caret-blink 1.25s ease-out infinite',
        },
    },

    plugins: [require('tailwindcss-animate')],
}
export default config
